import prettyBytes from 'pretty-bytes';

/**
 * 计算文件大小
 * @param byte 字节
 */
export function calculateFileSize(byte: number | string) {
  // 判断 byte 是否是 number 类型
  if (typeof byte === 'number') {
    return prettyBytes(byte);
  }

  if (!byte) return '--';

  // 如果不是 number 类型，尝试将其转换为 number 类型
  const byteToNumber = Number(byte);
  if (isNaN(byteToNumber)) {
    // 如果转换失败，返回 0
    return '--';
  }
  return prettyBytes(byteToNumber);
}
