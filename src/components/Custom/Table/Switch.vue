<script lang="ts" setup>
  import { Switch, type SwitchProps } from 'ant-design-vue';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { omit } from 'lodash-es';

  interface Props extends SwitchProps {
    api: (any) => Promise<void>;
    checked?: boolean;
    record?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    checked: false,
    api: () => Promise.resolve(),
    record: () => ({}),
  });

  const emit = defineEmits<{
    (e: 'success', checked: boolean): void;
  }>();
  const { loading, reload } = useApiLoading({
    api: props.api,
    immediate: false,
  });
  const handleChange: SwitchProps['onChange'] = (checked) => {
    reload({
      value: checked,
      record: props.record,
    }).then(() => {
      emit('success', checked as boolean);
    });
  };
</script>

<template>
  <Switch
    :checked="checked"
    @change="handleChange"
    :loading="loading"
    v-bind="omit(props, ['api', 'checked', 'loading', 'record'])"
  />
</template>
