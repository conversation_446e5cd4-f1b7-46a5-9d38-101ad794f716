<template>
  <div
    :class="[
      'upload-file-container',
      {
        'upload-file-container--disabled': disabled,
        'upload-file-container--preview': preview,
        'upload-file-container--drag-over': isDragOver,
      },
    ]"
    @drop.prevent="handleDrop"
    @dragover.prevent="handleDragOver"
    @dragenter.prevent="handleDragEnter"
    @dragleave.prevent="handleDragLeave"
  >
    <!-- 上传按钮区域 -->
    <div v-if="!preview" class="upload-file-trigger">
      <Button
        type="primary"
        :loading="uploadStatus === 'uploading'"
        :disabled="disabled"
        @click="handleUploadClick"
        class="upload-file-button"
      >
        <template #icon>
          <CloudUploadOutlined v-if="uploadStatus === 'normal'" />
        </template>
        <span v-if="uploadStatus === 'normal'">
          {{ t('component.upload.upload') }}
        </span>
        <span v-if="uploadStatus === 'uploading'" class="upload-progress-text">
          {{ t('component.upload.uploading') }}...
        </span>
      </Button>

      <!-- 上传进度条 -->
      <div v-if="uploadStatus === 'uploading'" class="upload-progress-bar">
        <Progress :percent="progress" :show-info="false" status="active" stroke-color="#1890ff" />
        <span class="upload-progress-percent">{{ progress }}%</span>
      </div>

      <!-- 拖拽提示 -->
      <div class="upload-drag-hint">
        <p class="upload-drag-text">
          <span v-if="!isDragOver">{{ t('component.upload.choose') }} 或 拖拽文件到此处</span>
          <span v-else class="drag-over-text">松开鼠标上传文件</span>
        </p>
        <p class="upload-accept-text">
          {{ t('component.upload.accept', [showAccept]) }}
        </p>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="upload-file-list">
      <TransitionGroup name="file-list" tag="div">
        <div v-for="(item, index) in fileList" :key="item.url || index" class="upload-file-item">
          <!-- 文件图标 -->
          <div class="file-icon">
            <Icon :icon="item.icon || 'vscode-icons:default-file'" :size="24" />
          </div>

          <!-- 文件信息 -->
          <div class="file-info">
            <div class="file-name" :title="item.fileName">
              {{ item.fileName }}
            </div>
            <div v-if="item.size" class="file-size">
              {{ formatFileSize(Number(item.size)) }}
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="file-actions">
            <template v-if="preview">
              <!-- 预览模式下的操作 -->
              <Tooltip v-if="item.previewTool" :title="t('component.upload.preview')">
                <Button
                  type="text"
                  size="small"
                  @click="handlePreview(item)"
                  class="action-button preview-button"
                >
                  <EyeOutlined />
                </Button>
              </Tooltip>
              <Tooltip v-if="item.url" :title="t('component.upload.download')">
                <Button
                  type="text"
                  size="small"
                  @click="handleDownload(item.url)"
                  class="action-button download-button"
                >
                  <DownloadOutlined />
                </Button>
              </Tooltip>
            </template>
            <template v-else>
              <!-- 编辑模式下的操作 -->
              <Tooltip :title="t('component.upload.del')">
                <Button
                  type="text"
                  size="small"
                  danger
                  @click="handleDelete(index)"
                  class="action-button delete-button"
                >
                  <DeleteOutlined />
                </Button>
              </Tooltip>
            </template>
          </div>
        </div>
      </TransitionGroup>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="acceptJoin"
      :multiple="multiple"
      class="upload-file-input"
      @change="handleFileChange"
    />

    <!-- 图片预览组件 -->
    <Image
      v-if="previewImageUrl"
      :preview="{
        visible: previewVisible,
        onVisibleChange: setPreviewVisible,
      }"
      :src="previewImageUrl"
      style="display: none"
    />
  </div>
</template>
<script setup lang="ts">
  import { ref, unref, computed } from 'vue';
  import {
    CloudUploadOutlined,
    DeleteOutlined,
    DownloadOutlined,
    EyeOutlined,
  } from '@ant-design/icons-vue';
  import { Progress, message, Button, Image, Tooltip } from 'ant-design-vue';
  import { apiGetUploadUrl, apiUploadFileToSignedUrl } from '@/api/admin/file';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { useI18n } from '@/hooks/web/useI18n';
  import { FileModel, FileResource } from './types/UploadFile';
  import { sleep } from '@/utils/other';
  import { PhotoCompress } from '@/utils/photoCompress';
  import { filter, map, flatten } from 'lodash-es';
  import Icon from '@/components/Icon/Icon.vue';
  import { downloadByUrl } from '@/utils/file/download';

  // 导入新的工具函数和常量
  import {
    FILE_TYPE_MAP,
    DEFAULT_FILE_TYPES,
    IMAGE_COMPRESS_SIZE_LIMIT,
  } from './constants/fileTypes';
  import { parseFileUrl, validateFile, formatFileSize, isImageFile } from './utils/fileUtils';
  import type { FileType } from './constants/fileTypes';

  defineOptions({ name: 'UploadFile' });

  const { t } = useI18n();

  type ValueState = string | string[] | FileResource[];

  interface Props {
    /** 组件值 */
    value?: ValueState;
    /** 最大文件数量，0表示不限制 */
    count?: number;
    /** 是否禁用 */
    disabled?: boolean;
    /** 支持的文件类型 */
    fileType?: FileType[];
    /** 是否为预览模式 */
    preview?: boolean;
    /** 数据格式类型：'string' 表示逗号分隔的字符串，'array' 表示字符串数组，'object' 表示对象数组 */
    valueType?: 'string' | 'array' | 'object';
    /** 是否支持多选 */
    multiple?: boolean;
    /** 文件大小限制（MB） */
    maxSize?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: undefined,
    count: 0,
    disabled: false,
    fileType: () => DEFAULT_FILE_TYPES,
    preview: false,
    valueType: 'string',
    multiple: false,
    maxSize: 0,
  });

  defineEmits(['change', 'update:value']);

  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', 'change');

  // 响应式状态
  const fileInputRef = ref<HTMLInputElement>();
  const progress = ref(0);
  const uploadStatus = ref<'normal' | 'uploading' | 'success' | 'error'>('normal');
  const previewImageUrl = ref('');
  const previewVisible = ref(false);
  const isDragOver = ref(false);

  // 计算属性
  const acceptList = computed(() => {
    const result = map(
      filter(FILE_TYPE_MAP, (item) => props.fileType.includes(item.name)),
      'value',
    );
    return flatten(result);
  });

  const acceptJoin = computed(() => {
    return acceptList.value?.length > 0 ? acceptList.value.join(',') : '';
  });

  const showAccept = computed(() => {
    return props.fileType?.length > 0 ? props.fileType.join('、') : '';
  });

  // 图片压缩实例
  const photoCompress = new PhotoCompress(2, 0.8);

  const fileList = computed({
    get(): FileModel[] {
      const _modelValue = unref(state);
      if (!_modelValue) return [];

      let fileModels: FileModel[] = [];

      // 根据 valueType 处理不同的数据格式
      if (props.valueType === 'string') {
        if (typeof _modelValue === 'string') {
          const urlList = _modelValue.split(',').filter(Boolean);
          fileModels = urlList.map((url: string) => parseFileUrl(url));
        }
      } else if (props.valueType === 'array') {
        if (Array.isArray(_modelValue)) {
          const urlList = _modelValue as string[];
          fileModels = urlList.map((url: string) => parseFileUrl(url));
        }
      } else if (props.valueType === 'object') {
        if (Array.isArray(_modelValue)) {
          const fileResources = _modelValue as FileResource[];
          fileModels = fileResources.map((item) => {
            const fileModel = parseFileUrl(item.url);
            // 从 FileResource 中恢复文件大小信息
            if (item.size) {
              fileModel.size = item.size;
            }
            return fileModel;
          });
        }
      }

      return fileModels;
    },
    set(val: FileModel[]) {
      // 根据 valueType 设置不同格式的值
      if (props.valueType === 'string') {
        state.value = val.map((item) => item.url).join(',');
      } else if (props.valueType === 'array') {
        state.value = val.map((item) => item.url);
      } else if (props.valueType === 'object') {
        state.value = val.map((item) => ({
          url: item.url,
          size: item.size ? item.size + '' : '',
        }));
      }
    },
  });

  // 设置预览可见性
  const setPreviewVisible = (visible: boolean) => {
    previewVisible.value = visible;
  };

  // 文件验证方法
  const validateFileInput = (file: File): boolean => {
    console.log('开始验证文件:', {
      name: file.name,
      type: file.type,
      size: file.size,
      acceptList: acceptList.value,
      showAccept: showAccept.value,
    });

    const validation = validateFile(file, acceptList.value);
    if (!validation.valid) {
      console.log('文件验证失败:', validation.error);
      if (validation.error === 'UNSUPPORTED_FILE_TYPE') {
        message.warn(t('component.upload.acceptUpload', [showAccept.value]));
      } else {
        message.warn(validation.error || '文件验证失败');
      }
      return false;
    }

    // 检查文件大小
    if (props.maxSize && file.size / 1024 / 1024 > props.maxSize) {
      console.log('文件大小超限:', file.size / 1024 / 1024, 'MB, 限制:', props.maxSize, 'MB');
      message.warn(t('component.upload.maxSizeMultiple', [props.maxSize]));
      return false;
    }

    console.log('文件验证通过');
    return true;
  };

  // 处理文件选择
  const handleFileChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const files = target.files;
    if (!files || files.length === 0) {
      console.warn('没有选择文件');
      return;
    }

    const file = files[0];
    if (!validateFileInput(file)) return;

    // 如果是图片文件且大于2MB，进行压缩
    if (isImageFile(file) && file.size > IMAGE_COMPRESS_SIZE_LIMIT) {
      photoCompress.compress(file, (result: File | null) => {
        if (result) {
          uploadFile(result);
        }
      });
    } else {
      uploadFile(file);
    }
  };

  // 上传文件
  const uploadFile = async (file: File) => {
    console.log('上传文件大小:', formatFileSize(file.size));
    try {
      progress.value = 0;
      uploadStatus.value = 'uploading';

      // 第一步：获取上传URL
      const uploadUrlRes = await apiGetUploadUrl({
        objectName: file.name,
        contentType: file.type,
      });

      // 第二步：使用获取到的预签名URL上传文件
      await apiUploadFileToSignedUrl(
        uploadUrlRes.url, // 预签名URL
        file, // 文件对象
        file.type, // 文件类型
        {
          onUploadProgress: (progressEvent: any) => {
            if (progressEvent.total) {
              progress.value = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
            } else {
              progress.value = 0;
            }
          },
        },
      );

      // 使用返回的文件URL
      const url = uploadUrlRes.fileUrl;
      const currentFiles = [...fileList.value];

      // 如果超过最大数量限制，删除最早的文件
      if (currentFiles.length >= props.count && props.count !== 0) {
        currentFiles.shift();
      }

      // 创建新的文件模型，包含文件大小信息
      const newFileModel = parseFileUrl(url);
      newFileModel.size = file.size + ''; // 保存原始文件大小
      currentFiles.push(newFileModel);

      await sleep(500);
      fileList.value = currentFiles;
      message.success(t('component.upload.uploadSuccess'));
    } catch (error) {
      console.error('上传失败:', error);
      message.error(t('component.upload.uploadError'));
    } finally {
      progress.value = 0;
      if (fileInputRef.value) {
        fileInputRef.value.value = '';
      }
      uploadStatus.value = 'normal';
    }
  };

  // 点击上传按钮
  const handleUploadClick = () => {
    fileInputRef.value?.click();
  };

  // 删除文件
  const handleDelete = (index: number) => {
    const currentFiles = [...fileList.value];
    currentFiles.splice(index, 1);
    fileList.value = currentFiles;
    message.success(t('component.upload.del') + '成功');
  };

  // 下载文件
  const handleDownload = (url: string) => {
    console.log('下载文件:', url);
    downloadByUrl({ url });
  };

  // 预览文件
  const handlePreview = (fileModel: FileModel) => {
    const { url, previewTool } = fileModel;
    if (!previewTool) {
      message.warn('暂不支持预览'); // TODO: 需要国际化
      return;
    }

    switch (previewTool) {
      case 'image':
        previewImageUrl.value = url;
        previewVisible.value = true;
        break;
      default:
        message.warn('暂不支持该类型文件的预览');
        break;
    }
  };

  // 拖拽相关处理函数
  const handleDragOver = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 设置拖拽效果
    if (e.dataTransfer) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDragEnter = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 只有在拖拽文件时才显示拖拽状态
    if (e.dataTransfer?.types.includes('Files')) {
      isDragOver.value = true;
    }
  };

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 检查是否真的离开了拖拽区域
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      isDragOver.value = false;
    }
  };

  const handleDrop = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    isDragOver.value = false;

    console.log('拖拽上传触发', { disabled: props.disabled, preview: props.preview });

    if (props.disabled || props.preview) {
      console.log('拖拽上传被阻止：组件禁用或预览模式');
      return;
    }

    const files = e.dataTransfer?.files;
    console.log('拖拽文件:', files);

    if (!files || files.length === 0) {
      console.log('没有检测到文件');
      message.warn('请拖拽文件到此处');
      return;
    }

    const file = files[0];
    console.log('处理文件:', { name: file.name, size: file.size, type: file.type });

    if (!validateFileInput(file)) {
      console.log('文件验证失败');
      return;
    }

    console.log('开始上传文件:', file.name);

    // 如果是图片文件且大于2MB，进行压缩
    if (isImageFile(file) && file.size > IMAGE_COMPRESS_SIZE_LIMIT) {
      console.log('图片文件需要压缩');
      photoCompress.compress(file, (result: File | null) => {
        if (result) {
          console.log('图片压缩完成，开始上传');
          uploadFile(result);
        } else {
          console.error('图片压缩失败');
          message.error('图片压缩失败');
        }
      });
    } else {
      uploadFile(file);
    }
  };
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .upload-file-trigger {
      padding: 16px;
    }

    .upload-file-item {
      padding: 8px 12px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }
  }

  .upload-file-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    transition: all 0.3s ease;

    &--disabled {
      opacity: 0.6;
      pointer-events: none;
    }

    &--preview {
      .upload-file-trigger {
        display: none;
      }
    }

    &--drag-over {
      .upload-file-trigger {
        transform: scale(1.02);
        border-color: @primary-color;
        background-color: @primary-1;
        box-shadow: 0 4px 12px rgb(24 144 255 / 20%);

        .upload-drag-text {
          color: @primary-color;
          font-weight: 500;
        }
      }
    }
  }

  .upload-file-trigger {
    display: flex;
    flex-direction: column;
    padding: 24px;
    transition: all 0.3s ease;
    border: 2px dashed @border-color-base;
    border-radius: 8px;
    background-color: @component-background;
    text-align: center;
    gap: 12px;

    &:hover {
      border-color: @primary-color;
    }
  }

  .upload-file-button {
    align-self: center;
    min-width: 120px;
  }

  .upload-progress-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .upload-progress-percent {
    min-width: 35px;
    color: @text-color-secondary;
    font-size: 12px;
    text-align: right;
  }

  .upload-drag-hint {
    .upload-drag-text {
      margin: 8px 0 4px;
      transition: all 0.3s ease;
      color: @text-color-base;
      font-size: 14px;

      .drag-over-text {
        color: @primary-color;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .upload-accept-text {
      margin: 0;
      color: @text-color-secondary;
      font-size: 12px;
    }
  }

  .upload-file-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .upload-file-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    transition: all 0.3s ease;
    border: 1px solid @border-color-base;
    border-radius: 6px;
    background-color: @component-background;
    gap: 12px;

    &:hover {
      border-color: @primary-color;
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    }
  }

  .file-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
  }

  .file-info {
    flex: 1;
    min-width: 0;

    .file-name {
      margin-bottom: 2px;
      overflow: hidden;
      color: @text-color-base;
      font-size: 14px;
      font-weight: 500;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-size {
      color: @text-color-secondary;
      font-size: 12px;
    }
  }

  .file-actions {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    gap: 4px;
  }

  .action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    transition: all 0.2s ease;
    border-radius: 4px;

    &.preview-button {
      color: @primary-color;

      &:hover {
        background-color: @primary-1;
      }
    }

    &.download-button {
      color: @success-color;

      &:hover {
        background-color: @success-color;
      }
    }

    &.delete-button {
      color: @error-color;

      &:hover {
        background-color: @error-color;
      }
    }
  }

  .upload-file-input {
    display: none !important;
  }

  // 文件列表动画
  .file-list-enter-active,
  .file-list-leave-active {
    transition: all 0.3s ease;
  }

  .file-list-enter-from {
    transform: translateY(-10px);
    opacity: 0;
  }

  .file-list-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .file-list-move {
    transition: transform 0.3s ease;
  }
</style>
