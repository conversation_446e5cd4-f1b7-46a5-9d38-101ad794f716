<template>
  <div class="upload-example-container">
    <h2>UploadFile 组件文件大小保存示例</h2>
    
    <div class="example-section">
      <h3>1. 对象模式 - 支持文件大小保存</h3>
      <p class="description">
        当 <code>valueType="object"</code> 时，组件会保存文件的大小信息到 FileResource.size 字段中。
      </p>
      
      <UploadFile
        v-model:value="objectFiles"
        value-type="object"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX', 'JPG', 'PNG']"
        :max-size="10"
        :count="3"
      />
      
      <div class="data-display">
        <h4>当前数据结构：</h4>
        <pre>{{ JSON.stringify(objectFiles, null, 2) }}</pre>
      </div>
    </div>

    <div class="example-section">
      <h3>2. 预览模式 - 显示文件大小</h3>
      <p class="description">
        在预览模式下，可以看到文件大小信息的显示效果。
      </p>
      
      <UploadFile
        v-model:value="previewFiles"
        value-type="object"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX', 'JPG', 'PNG']"
        preview
      />
    </div>

    <div class="example-section">
      <h3>3. 字符串模式 - 不保存文件大小</h3>
      <p class="description">
        当 <code>valueType="string"</code> 时，只保存文件URL，不保存大小信息。
      </p>
      
      <UploadFile
        v-model:value="stringFiles"
        value-type="string"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX', 'JPG', 'PNG']"
        :max-size="10"
        :count="3"
      />
      
      <div class="data-display">
        <h4>当前数据：</h4>
        <pre>{{ stringFiles }}</pre>
      </div>
    </div>

    <div class="example-section">
      <h3>4. 数组模式 - 不保存文件大小</h3>
      <p class="description">
        当 <code>valueType="array"</code> 时，保存文件URL数组，不保存大小信息。
      </p>
      
      <UploadFile
        v-model:value="arrayFiles"
        value-type="array"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX', 'JPG', 'PNG']"
        :max-size="10"
        :count="3"
      />
      
      <div class="data-display">
        <h4>当前数据：</h4>
        <pre>{{ JSON.stringify(arrayFiles, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadFile from '../UploadFile.vue';
import type { FileResource } from '../types/UploadFile';

// 对象模式 - 支持文件大小保存
const objectFiles = ref<FileResource[]>([]);

// 预览模式的示例数据
const previewFiles = ref<FileResource[]>([
  {
    url: 'https://example.com/sample-document.pdf',
    size: '1048576' // 1MB
  },
  {
    url: 'https://example.com/sample-spreadsheet.xlsx',
    size: '2097152' // 2MB
  },
  {
    url: 'https://example.com/sample-image.jpg',
    size: '512000' // 500KB
  }
]);

// 字符串模式 - 不保存文件大小
const stringFiles = ref<string>('');

// 数组模式 - 不保存文件大小
const arrayFiles = ref<string[]>([]);
</script>

<style scoped>
.upload-example-container {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 48px;
  padding: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1890ff;
  font-size: 18px;
}

.description {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f0f8ff;
  border-left: 4px solid #1890ff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
}

.description code {
  padding: 2px 6px;
  background-color: #f5f5f5;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.data-display {
  margin-top: 24px;
  padding: 16px;
  background-color: #f8f8f8;
  border-radius: 6px;
}

.data-display h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #333;
  font-size: 14px;
}

.data-display pre {
  margin: 0;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
