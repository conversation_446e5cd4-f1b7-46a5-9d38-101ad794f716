<template>
  <div class="test-upload-container">
    <h3>测试文件上传大小保存功能</h3>
    
    <div class="test-section">
      <h4>1. 字符串模式 (valueType="string")</h4>
      <UploadFile
        v-model:value="stringValue"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX']"
        value-type="string"
        :max-size="10"
      />
      <p>当前值: {{ stringValue }}</p>
    </div>

    <div class="test-section">
      <h4>2. 数组模式 (valueType="array")</h4>
      <UploadFile
        v-model:value="arrayValue"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX']"
        value-type="array"
        :max-size="10"
      />
      <p>当前值: {{ arrayValue }}</p>
    </div>

    <div class="test-section">
      <h4>3. 对象模式 (valueType="object") - 支持文件大小保存</h4>
      <UploadFile
        v-model:value="objectValue"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX']"
        value-type="object"
        :max-size="10"
      />
      <p>当前值: {{ JSON.stringify(objectValue, null, 2) }}</p>
    </div>

    <div class="test-section">
      <h4>4. 预览模式测试</h4>
      <UploadFile
        v-model:value="previewValue"
        :file-type="['PDF', 'DOC', 'DOCX', 'XLS', 'XLSX']"
        value-type="object"
        preview
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadFile from './UploadFile.vue';
import type { FileResource } from './types/UploadFile';

// 测试不同的数据格式
const stringValue = ref<string>('');
const arrayValue = ref<string[]>([]);
const objectValue = ref<FileResource[]>([]);

// 预设一些测试数据
const previewValue = ref<FileResource[]>([
  {
    url: 'https://example.com/test1.pdf',
    size: '1024000' // 1MB
  },
  {
    url: 'https://example.com/test2.docx',
    size: '2048000' // 2MB
  }
]);
</script>

<style scoped>
.test-upload-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fafafa;
}

.test-section h4 {
  margin-top: 0;
  color: #1890ff;
}

.test-section p {
  margin-top: 16px;
  padding: 12px;
  border-radius: 4px;
  background-color: #f0f0f0;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}
</style>
