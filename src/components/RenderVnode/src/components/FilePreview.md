# FilePreview 文件预览组件

## 概述

FilePreview 是一个用于预览和下载文件的 Vue 组件，支持多种显示模式，可以处理单个或多个文件的展示。

## 功能特性

- ✅ 支持三种预览模式：平面列表、Popover 弹出框、Modal 模态框
- ✅ 自动解析文件名和文件类型图标
- ✅ 支持文件下载功能
- ✅ 支持文件大小显示
- ✅ 响应式设计，适配移动端
- ✅ 支持最大文件数量限制
- ✅ 集成到 RenderVnode 系统中

## Props 参数

| 参数名   | 类型                           | 默认值 | 说明                                    |
| -------- | ------------------------------ | ------ | --------------------------------------- |
| value    | string                         | ''     | 文件URL，支持单个URL或逗号分隔的多个URL |
| mode     | 'list' \| 'popover' \| 'modal' | 'list' | 预览模式                                |
| maxCount | number                         | 0      | 最大显示文件数量，0表示不限制           |

## 预览模式说明

### 1. list 模式（平面列表）

- 适用于单个或少量文件的展示
- 简洁设计：只显示文件名和下载按钮，无边框和装饰
- 适合在表格或详情页中使用

### 2. popover 模式（弹出框）

- 适用于多个文件的展示
- 显示一个触发按钮，点击后弹出文件列表
- 节省页面空间，适合在列表页中使用

### 3. modal 模式（模态框）

- 适用于大量文件的展示
- 在模态框中展示文件列表，提供更大的展示空间
- 适合需要详细查看文件信息的场景

## 使用方式

### 1. 直接使用组件

```vue
<template>
  <!-- 平面列表模式 -->
  <FilePreview :value="fileUrls" mode="list" />

  <!-- Popover 弹出框模式 -->
  <FilePreview :value="fileUrls" mode="popover" />

  <!-- Modal 模态框模式 -->
  <FilePreview :value="fileUrls" mode="modal" />

  <!-- 限制显示数量 -->
  <FilePreview :value="fileUrls" mode="list" :max-count="3" />
</template>

<script setup>
  import { FilePreview } from '@/components/RenderVnode';

  const fileUrls =
    'https://example.com/file1.pdf?filename=文档1.pdf,https://example.com/file2.xlsx?filename=表格2.xlsx';
</script>
```

### 2. 使用 render 函数

```typescript
import { filePreview } from '@/components/RenderVnode';

// 在表格列定义中使用
const columns = [
  {
    title: '附件',
    dataIndex: 'files',
    customRender: ({ text }) => filePreview(text, 'popover'),
  },
];

// 在描述列表中使用
const schema = [
  {
    field: 'attachments',
    label: '相关文件',
    render: (val) => filePreview(val, 'list'),
  },
];
```

### 3. 在 RenderVnode 中使用

```vue
<template>
  <RenderVnode :h="renderFilePreview" />
</template>

<script setup>
  import { computed } from 'vue';
  import { RenderVnode, filePreview } from '@/components/RenderVnode';

  const fileUrls = 'https://example.com/file.pdf?filename=示例文档.pdf';

  const renderFilePreview = computed(() => {
    return filePreview(fileUrls, 'popover');
  });
</script>
```

## 文件URL格式

组件支持以下URL格式：

```
// 单个文件
https://example.com/files/document.pdf?filename=项目文档.pdf

// 多个文件（逗号分隔）
https://example.com/files/doc1.pdf?filename=文档1.pdf,https://example.com/files/doc2.xlsx?filename=表格2.xlsx
```

文件名解析规则：

1. 优先从URL查询参数中获取包含 '.' 的值作为文件名
2. 如果查询参数中没有合适的文件名，则从URL路径中获取
3. 根据文件扩展名自动匹配对应的图标

## 样式定制

组件使用 Less 编写样式，支持主题变量：

- `@primary-color`: 主色调
- `@text-color-base`: 基础文本颜色
- `@text-color-secondary`: 次要文本颜色
- `@border-color-base`: 边框颜色
- `@component-background`: 组件背景色

## 注意事项

1. 文件下载功能依赖 `downloadByUrl` 工具函数
2. 文件图标和类型解析依赖 `parseFileUrl` 工具函数
3. 组件已集成国际化支持，使用 `useI18n` hook
4. 响应式设计在移动端会自动调整样式
5. 文件名过长时会自动截断并显示省略号，鼠标悬停可查看完整文件名

## 依赖项

- ant-design-vue: UI 组件库
- @ant-design/icons-vue: 图标库
- @/components/Icon: 项目图标组件
- @/utils/file/download: 文件下载工具
- @/hooks/web/useI18n: 国际化 hook
