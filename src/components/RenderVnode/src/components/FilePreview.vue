<template>
  <div class="file-preview-container">
    <!-- 平面列表模式 -->
    <div v-if="mode === 'list'" class="file-preview-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-preview-item">
        <!-- 文件图标 -->
        <div class="file-icon">
          <Icon :icon="file.icon || 'vscode-icons:default-file'" :size="20" />
        </div>

        <!-- 文件信息 -->
        <div class="file-info">
          <div class="file-name" :title="file.fileName">
            {{ file.fileName }}
          </div>
          <div v-if="file.size" class="file-size">
            {{ formatFileSize(Number(file.size)) }}
          </div>
        </div>

        <!-- 下载按钮 -->
        <div class="file-actions">
          <Tooltip :title="t('component.upload.download')">
            <Button
              type="text"
              size="small"
              @click="handleDownload(file.url, file.fileName)"
              class="action-button download-button"
            >
              <DownloadOutlined />
            </Button>
          </Tooltip>
        </div>
      </div>
    </div>

    <!-- Popover 模式 -->
    <Popover v-else-if="mode === 'popover'" placement="topLeft" trigger="click">
      <template #content>
        <div class="file-preview-popover-content">
          <div class="popover-header">
            <span class="popover-title">文件列表</span>
            <span class="file-count">({{ fileList.length }}个文件)</span>
          </div>
          <div class="popover-file-list">
            <div v-for="(file, index) in fileList" :key="index" class="popover-file-item">
              <div class="file-icon">
                <Icon :icon="file.icon || 'vscode-icons:default-file'" :size="16" />
              </div>
              <div class="file-info">
                <div class="file-name" :title="file.fileName">
                  {{ file.fileName }}
                </div>
                <div v-if="file.size" class="file-size">
                  {{ formatFileSize(Number(file.size)) }}
                </div>
              </div>
              <Button
                type="text"
                size="small"
                @click="handleDownload(file.url, file.fileName)"
                class="download-btn"
              >
                <DownloadOutlined />
              </Button>
            </div>
          </div>
        </div>
      </template>
      <Button type="link" class="file-preview-trigger">
        <FileTextOutlined />
        查看文件 ({{ fileList.length }})
      </Button>
    </Popover>

    <!-- Modal 模式 -->
    <div v-else-if="mode === 'modal'">
      <Button type="link" @click="showModal = true" class="file-preview-trigger">
        <FileTextOutlined />
        查看文件 ({{ fileList.length }})
      </Button>

      <Modal
        v-model:open="showModal"
        title="文件列表"
        :footer="null"
        width="600px"
        class="file-preview-modal"
      >
        <div class="modal-file-list">
          <div v-for="(file, index) in fileList" :key="index" class="modal-file-item">
            <div class="file-icon">
              <Icon :icon="file.icon || 'vscode-icons:default-file'" :size="24" />
            </div>
            <div class="file-info">
              <div class="file-name" :title="file.fileName">
                {{ file.fileName }}
              </div>
              <div v-if="file.size" class="file-size">
                {{ formatFileSize(Number(file.size)) }}
              </div>
            </div>
            <div class="file-actions">
              <Button
                type="primary"
                size="small"
                @click="handleDownload(file.url, file.fileName)"
                class="download-button"
              >
                <DownloadOutlined />
                下载
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { Button, Tooltip, Popover, Modal, message } from 'ant-design-vue';
  import { DownloadOutlined, FileTextOutlined } from '@ant-design/icons-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { downloadByUrl } from '@/utils/file/download';
  import { useI18n } from '@/hooks/web/useI18n';
  import { parseFileUrl, formatFileSize } from '@/components/Form/src/extend/utils/fileUtils';
  import type { FileModel } from '@/components/Form/src/extend/types/UploadFile';

  defineOptions({ name: 'FilePreview' });

  const { t } = useI18n();

  interface Props {
    /** 文件URL，支持单个URL或逗号分隔的多个URL */
    value?: string;
    /** 预览模式：list-平面列表，popover-弹出框，modal-模态框 */
    mode?: 'list' | 'popover' | 'modal';
    /** 最大显示文件数量，0表示不限制 */
    maxCount?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: '',
    mode: 'list',
    maxCount: 0,
  });

  // 响应式状态
  const showModal = ref(false);

  // 计算文件列表
  const fileList = computed((): FileModel[] => {
    if (!props.value) return [];

    const urlList = props.value.split(',').filter(Boolean);
    let files = urlList.map((url: string) => parseFileUrl(url.trim()));

    // 如果设置了最大数量限制
    if (props.maxCount > 0 && files.length > props.maxCount) {
      files = files.slice(0, props.maxCount);
    }

    return files;
  });

  // 下载文件
  const handleDownload = (url: string, fileName?: string) => {
    try {
      const success = downloadByUrl({
        url,
        fileName: fileName || url.substring(url.lastIndexOf('/') + 1),
      });

      if (!success) {
        message.error('下载失败，请检查浏览器设置');
      }
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败');
    }
  };
</script>

<style lang="less" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .file-preview-item {
      padding: 8px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }

    .file-preview-popover-content {
      max-width: 300px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .file-preview-item {
      padding: 8px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }

    .file-preview-popover-content {
      max-width: 300px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .file-preview-item {
      padding: 8px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }

    .file-preview-popover-content {
      max-width: 300px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .file-preview-item {
      padding: 8px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }

    .file-preview-popover-content {
      max-width: 300px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .file-preview-item {
      padding: 8px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }

    .file-preview-popover-content {
      max-width: 300px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .file-preview-item {
      padding: 8px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }

    .file-preview-popover-content {
      max-width: 300px;
    }
  }
  // 响应式设计
  @media (max-width: 768px) {
    .file-preview-item {
      padding: 8px;
      gap: 8px;
    }

    .file-info .file-name {
      font-size: 13px;
    }

    .file-preview-popover-content {
      max-width: 300px;
    }
  }

  .file-preview-container {
    width: 100%;
  }

  // 平面列表样式
  .file-preview-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .file-preview-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    transition: all 0.3s ease;
    border: 1px solid @border-color-base;
    border-radius: 6px;
    background-color: @component-background;
    gap: 12px;

    &:hover {
      border-color: @primary-color;
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    }
  }

  .file-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
  }

  .file-info {
    flex: 1;
    min-width: 0;

    .file-name {
      margin-bottom: 2px;
      overflow: hidden;
      color: @text-color-base;
      font-size: 14px;
      font-weight: 500;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-size {
      color: @text-color-secondary;
      font-size: 12px;
    }
  }

  .file-actions {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    gap: 4px;
  }

  .action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    transition: all 0.2s ease;
    border-radius: 4px;

    &.download-button {
      color: @success-color;

      &:hover {
        background-color: @success-background-color;
      }
    }
  }

  // 触发按钮样式
  .file-preview-trigger {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 0;
    color: @primary-color;
    text-decoration: underline;

    &:hover {
      color: @primary-color-hover;
    }
  }

  // Popover 内容样式
  .file-preview-popover-content {
    max-width: 400px;

    .popover-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid @border-color-split;
      gap: 8px;

      .popover-title {
        color: @text-color-base;
        font-weight: 500;
      }

      .file-count {
        color: @text-color-secondary;
        font-size: 12px;
      }
    }

    .popover-file-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .popover-file-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      gap: 8px;

      &:not(:last-child) {
        border-bottom: 1px solid @border-color-split;
      }

      .file-info {
        flex: 1;
        min-width: 0;

        .file-name {
          margin-bottom: 2px;
          overflow: hidden;
          color: @text-color-base;
          font-size: 13px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          color: @text-color-secondary;
          font-size: 11px;
        }
      }

      .download-btn {
        flex-shrink: 0;
        color: @success-color;

        &:hover {
          background-color: @success-background-color;
        }
      }
    }
  }

  // Modal 内容样式
  .modal-file-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .modal-file-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    gap: 12px;

    &:not(:last-child) {
      border-bottom: 1px solid @border-color-split;
    }

    .file-info {
      flex: 1;
      min-width: 0;

      .file-name {
        margin-bottom: 4px;
        overflow: hidden;
        color: @text-color-base;
        font-size: 14px;
        font-weight: 500;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-size {
        color: @text-color-secondary;
        font-size: 12px;
      }
    }

    .file-actions {
      flex-shrink: 0;

      .download-button {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
</style>
