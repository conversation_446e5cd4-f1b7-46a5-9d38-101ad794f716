<template>
  <div class="file-preview-example">
    <h2>文件预览组件示例</h2>

    <div class="example-section">
      <h3>1. 平面列表模式 (mode="list")</h3>
      <p>简洁模式：只显示文件名和下载按钮，适用于单个或少量文件</p>
      <FilePreview :value="sampleFileUrls" mode="list" />
    </div>

    <div class="example-section">
      <h3>2. Popover 弹出框模式 (mode="popover")</h3>
      <p>适用于多个文件，点击按钮弹出文件列表</p>
      <FilePreview :value="sampleFileUrls" mode="popover" />
    </div>

    <div class="example-section">
      <h3>3. Modal 模态框模式 (mode="modal")</h3>
      <p>适用于大量文件，在模态框中展示文件列表</p>
      <FilePreview :value="sampleFileUrls" mode="modal" />
    </div>

    <div class="example-section">
      <h3>4. 限制显示数量 (maxCount=2)</h3>
      <p>只显示前2个文件</p>
      <FilePreview :value="sampleFileUrls" mode="list" :max-count="2" />
    </div>

    <div class="example-section">
      <h3>5. 单个文件</h3>
      <p>单个文件的展示效果</p>
      <FilePreview :value="singleFileUrl" mode="list" />
    </div>

    <div class="example-section">
      <h3>6. 使用 render 函数</h3>
      <p>通过 filePreview 函数创建的组件</p>
      <RenderVnode :h="renderFilePreview" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import FilePreview from './FilePreview.vue';
  import RenderVnode from '../RenderVnode';
  import { filePreview } from '../RenderH';

  // 示例文件URL（模拟数据）
  const sampleFileUrls =
    'https://example.com/files/document.pdf?filename=项目文档.pdf,https://example.com/files/image.jpg?filename=示例图片.jpg,https://example.com/files/excel.xlsx?filename=数据表格.xlsx,https://example.com/files/word.docx?filename=需求文档.docx';

  const singleFileUrl = 'https://example.com/files/report.pdf?filename=月度报告.pdf';

  // 使用 render 函数创建组件
  const renderFilePreview = computed(() => {
    return filePreview(sampleFileUrls, 'popover');
  });
</script>

<style lang="less" scoped>
  .file-preview-example {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
  }

  .example-section {
    margin-bottom: 32px;
    padding: 16px;
    border: 1px solid @border-color-base;
    border-radius: 8px;
    background-color: @component-background;

    h3 {
      margin-top: 0;
      margin-bottom: 8px;
      color: @text-color-base;
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin-bottom: 16px;
      color: @text-color-secondary;
      font-size: 14px;
    }
  }

  h2 {
    margin-bottom: 24px;
    color: @text-color-base;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
  }
</style>
