import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';

const firm: AppRouteModule = {
  path: '/firm',
  name: 'Firm',
  component: LAYOUT,
  redirect: '/firm/list',
  meta: {
    orderNo: 30,
    icon: 'ant-design:cloud-upload-outlined',
    title: '固件管理',
  },
  children: [
    {
      path: 'list',
      name: 'FirmList',
      component: () => import('@/views/firm/list.vue'),
      meta: {
        title: '固件列表',
        icon: 'ant-design:unordered-list-outlined',
      },
    },
    {
      path: 'list/:id',
      name: 'FirmInfo',
      component: () => import('@/views/firm/info.vue'),
      meta: {
        title: '固件详情',
        hideMenu: true,
        currentActiveMenu: '/firm/list',
      },
    },
  ],
};

export default firm;
