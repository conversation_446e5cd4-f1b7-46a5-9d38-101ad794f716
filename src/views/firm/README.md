# 固件升级管理

## 功能概述

固件升级管理页面用于管理设备固件的版本控制和升级任务。

## 页面功能

### 1. 固件列表 (list.vue)

#### 主要功能

- **固件列表展示**: 显示所有固件的基本信息
- **搜索筛选**: 支持按固件名称、设备型号、产品型号、创建时间范围进行筛选
- **固件管理**: 支持添加、编辑、删除固件
- **详情查看**: 可查看固件的详细信息

#### 表格字段

- **固件名称**: 固件的名称标识
- **固件版本**: 固件的版本号
- **支持型号**: 该固件支持的产品型号列表
- **创建时间**: 固件的创建时间
- **固件描述**: 固件的详细描述信息

#### 操作功能

- **查看详情**: 跳转到固件详情页面
- **编辑固件**: 修改固件信息
- **删除固件**: 删除指定固件（需确认）

### 2. 固件详情 (info.vue)

显示固件的详细信息，包括基本信息和相关统计数据。

## 技术实现

### API 接口

- `apiFirmwareStorePageInfo`: 获取固件分页列表
- `apiFirmwareStoreInfo`: 获取固件详情
- `apiAddFirmwareStore`: 新增固件
- `apiUpdateFirmwareStore`: 更新固件信息
- `apiDeleteFirmwareStore`: 删除固件

### 数据模型

- `DmsFirmwareStore`: 固件存储数据模型
- `FirmwareStorePageParams`: 固件分页查询参数
- `FirmwareStorePageResult`: 固件分页响应数据

### 组件结构

```
src/views/firm/
├── list.vue          # 固件列表页面
├── list.schema.ts    # 表格和表单配置
├── info.vue          # 固件详情页面
├── info.schema.ts    # 固件详情页面配置
└── README.md         # 说明文档
```

## 使用说明

1. **查看固件列表**: 进入页面后自动加载固件列表
2. **搜索固件**: 使用顶部搜索表单进行筛选
3. **添加固件**: 点击"添加固件"按钮，填写固件信息
4. **编辑固件**: 点击操作列的编辑按钮
5. **删除固件**: 点击操作列的删除按钮，确认后删除
6. **查看详情**: 点击操作列的详情按钮

## 注意事项

- 固件文件支持 .bin、.hex、.fw 格式
- 产品ID在保存时会自动转换为逗号分隔的字符串格式
- 编辑时会自动将产品ID字符串转换回数组格式
- 删除操作需要用户确认，防止误删
