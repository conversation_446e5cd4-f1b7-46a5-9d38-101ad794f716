<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="固件详情">
    <template #header>
      <BasicUser
        :username="get(apiResult, 'name')"
        :description="get(apiResult, 'version')"
        showDes
      >
        <template #extra>
          <a-button type="primary">添加升级任务</a-button>
        </template>
      </BasicUser>
    </template>

    <div class="flex flex-col gap-4">
      <!-- 固件详情信息卡片 -->
      <a-card :tabList="tabList" v-model:activeTabKey="activeTabKey" @tab-change="onTabChange">
        <Description
          v-if="activeTabKey === 'info'"
          :data="apiResult"
          :schema="basicInfoSchema"
          :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }"
          :bordered="false"
        />
      </a-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import BasicUser from '@/components/Custom/BasicUser.vue';
  import { Description } from '@/components/Description';
  import { get } from 'lodash-es';
  import { apiFirmwareStoreInfo } from '@/api/op/firm';
  import { basicInfoSchema } from './info.schema';
  import { ref } from 'vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const tabList = [
    {
      key: 'info',
      tab: '固件详情',
    },
    {
      key: 'upgrade',
      tab: '升级任务',
    },
    {
      key: 'log',
      tab: '升级记录',
    },
  ];

  const activeTabKey = ref('info');
  const {
    reload: _reload,
    loading,
    apiResult,
  } = useApiLoading({
    api: async () => {
      const id = params.id as string;
      if (!id) return {};
      return await apiFirmwareStoreInfo(id);
    },
    params,
  });

  const onTabChange = (key: string) => {
    activeTabKey.value = key;
  };
</script>

<style lang="less" scoped></style>
