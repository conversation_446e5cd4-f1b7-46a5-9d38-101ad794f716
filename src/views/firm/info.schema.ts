import { DescItem } from '@/components/Description';
import { filePreview } from '@/components/RenderVnode';
import { calculateFileSize } from '@/utils/calculateFileSize';

// 固件基础信息
export const basicInfoSchema: DescItem[] = [
  {
    field: 'name',
    label: '固件名称',
  },
  {
    field: 'productList',
    label: '支持设备型号',
    render: (_, record) => {
      if (record.productList && record.productList.length > 0) {
        return record.productList.map((product: any) => product.model).join(', ');
      }
      return '-';
    },
  },
  {
    field: 'version',
    label: '固件版本',
  },
  {
    field: 'signType',
    label: '签名方式',
  },
  {
    field: 'sign',
    label: '签名',
  },
  {
    field: 'file',
    label: '固件文件',
    render: (val: string) => {
      return filePreview(val, 'popover');
    },
  },
  {
    field: 'size',
    label: '文件大小',
    render: (val: string) => {
      return calculateFileSize(val);
    },
  },
  {
    field: 'remark',
    label: '固件描述',
  },
  {
    field: 'createTime',
    label: '创建时间',
  },
  {
    field: 'createBy',
    label: '创建人',
  },
];
